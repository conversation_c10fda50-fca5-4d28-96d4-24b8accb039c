
from .logger import get_logger
from .config import get_config, get_api_config
from .session import get_session
import pandas as pd

# 获取当前模块的日志器
logger = get_logger(__name__)

def get_data_fields_cached(search: str = '', force_refresh: bool = False):
    """
    按条件获取 data_fields，支持数据库缓存

    Args:
        search (str): 搜索关键词
        force_refresh (bool): 是否强制刷新（通过接口获取），默认False

    Returns:
        pd.DataFrame: 数据字段DataFrame
    """
    from .db import get_db_connection, batch_insert_data_fields
    import json

    # 获取配置参数
    dataset_id = get_config("dataset_id")
    region = get_config("region")
    universe = get_config("universe")
    delay = get_config("delay")
    instrument_type = get_config("instrument_type")

    logger.info(f'[get_data_fields_cached] 参数: dataset_id={dataset_id}, region={region}, universe={universe}, delay={delay}, instrument_type={instrument_type}, search={search}, force_refresh={force_refresh}')

    # 如果不强制刷新，先尝试从数据库获取
    if not force_refresh:
        try:
            cached_data = _query_data_fields_from_db(dataset_id, region, universe, delay, instrument_type, search)
            if cached_data is not None and len(cached_data) > 0:
                logger.info(f'[get_data_fields_cached] 从数据库获取到 {len(cached_data)} 条记录')
                return cached_data
            else:
                logger.info('[get_data_fields_cached] 数据库中未找到匹配的数据，将从API获取')
        except Exception as e:
            logger.warning(f'[get_data_fields_cached] 从数据库查询失败: {e}，将从API获取')

    # 从API获取数据
    logger.info('[get_data_fields_cached] 从API获取数据...')
    data_fields_df = get_data_fields(search)

    # 将数据保存到数据库
    if data_fields_df is not None and len(data_fields_df) > 0:
        try:
            # 转换DataFrame为字典列表，以便保存到数据库
            fields_list = data_fields_df.to_dict('records')
            batch_insert_data_fields(fields_list)
            logger.info(f'[get_data_fields_cached] 已将 {len(fields_list)} 条记录保存到数据库')
        except Exception as e:
            logger.error(f'[get_data_fields_cached] 保存到数据库失败: {e}')

    return data_fields_df


def _query_data_fields_from_db(dataset_id: str, region: str, universe: str, delay: int, instrument_type: str, search: str = ''):
    """
    从数据库查询 data_fields

    Args:
        dataset_id (str): 数据集ID
        region (str): 地区
        universe (str): 股票池
        delay (int): 延迟天数
        instrument_type (str): 工具类型
        search (str): 搜索关键词

    Returns:
        pd.DataFrame or None: 查询结果，如果没有找到返回None
    """
    from .db import get_db_connection

    try:
        with get_db_connection() as conn:
            # 构建查询条件
            where_conditions = []
            params = []

            if dataset_id:
                where_conditions.append("dataset_id = ?")
                params.append(dataset_id)

            if region:
                where_conditions.append("region = ?")
                params.append(region)

            if universe:
                where_conditions.append("universe = ?")
                params.append(universe)

            if delay is not None:
                where_conditions.append("delay = ?")
                params.append(delay)

            # 搜索条件（如果提供了搜索关键词）
            if search:
                where_conditions.append("(field_id LIKE ? OR description LIKE ?)")
                search_pattern = f"%{search}%"
                params.extend([search_pattern, search_pattern])

            # 构建完整的SQL查询
            base_sql = "SELECT * FROM data_fields"
            if where_conditions:
                sql = f"{base_sql} WHERE {' AND '.join(where_conditions)}"
            else:
                sql = base_sql

            logger.debug(f'[_query_data_fields_from_db] SQL: {sql}, params: {params}')

            # 执行查询
            df = pd.read_sql_query(sql, conn, params=params)

            if len(df) > 0:
                logger.info(f'[_query_data_fields_from_db] 查询到 {len(df)} 条记录')
                return df
            else:
                logger.info('[_query_data_fields_from_db] 未查询到匹配的记录')
                return None

    except Exception as e:
        logger.error(f'[_query_data_fields_from_db] 数据库查询失败: {e}')
        return None

def get_data_fields( search: str = ''):
    dataset_id = get_config("dataset_id")
    region = get_config("region")
    universe = get_config("universe")
    delay = get_config("delay")
    instrument_type = get_config("instrument_type")

    url_template = get_url_template(dataset_id, region, universe, delay, instrument_type, search)

    data_fields = get_session().get(url_template.format(x=0))
    # print(f'data_fields is ${data_fields}')
    data_fields_json = data_fields.json()
    logger.info(f'[get_data_fields] data_fields_json is ${str(data_fields_json)[:100]}......')
    count = data_fields_json['count']
    logger.info(f'[get_data_fields] count is ${count}')

    # 分页循环，加载所有的 数据字段
    data_fields_list = []
    for x in range(0, count, 50):
        logger.info(f'[get_data_fields] x is ${x}')
        data_fields = get_session().get(url_template.format(x=x))
        data_fields_json = data_fields.json()
        data_fields_list.append(data_fields_json['results'])

    logger.info(f'[get_data_fields] data_fields_list length={len(data_fields_list)}个数组')

    # 变成二维表格
    data_fields_list_flat = [
        item for sublist in data_fields_list for item in sublist]

    logger.info(f'[get_data_fields] data_fields_list_flat 共获取 data_field {len(data_fields_list_flat)}个')

    # 打印 json 数组 data_fields_list_flat
    for item in data_fields_list_flat:
        logger.info(item)

    data_fields_df = pd.DataFrame(data_fields_list_flat)
    logger.info(f'[get_data_fields] data_fields_df is ${data_fields_df}')
    return data_fields_df

def get_url_template(dataset_id: str, region: str = 'USA', universe: str = 'TOP3000', delay: int = 1,
                    instrument_type: str = 'EQUITY', search: str = ''):
    api_config = get_api_config()
    base_url = api_config.get('datafields', '')
    logger.info(f'[data_fields] base_url is ${base_url}')

    if len(search) == 0:
        url_template = f"{base_url}?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
    else:
        url_template = f"{base_url}?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
    logger.info(f'[data_fields] url_template is ${url_template}')
    return url_template
