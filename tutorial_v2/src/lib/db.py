#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
使用 SQLite 创建和管理本地数据库
"""

import sqlite3
import os
from pathlib import Path
from .logger import get_logger

# 获取当前模块的日志器
logger = get_logger(__name__)

# 数据库文件路径
DB_DIR = Path(__file__).parent.parent.parent / "db"
DB_FILE = DB_DIR / "wq.db"


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.db_path = DB_FILE
        self.db_dir = DB_DIR
        
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        try:
            self.db_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"数据库目录已确保存在: {self.db_dir}")
            return True
        except Exception as e:
            logger.error(f"创建数据库目录失败: {e}")
            return False
    
    def database_exists(self):
        """检查数据库文件是否存在"""
        exists = self.db_path.exists()
        logger.info(f"数据库文件存在检查: {self.db_path} -> {exists}")
        return exists
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            # 启用外键约束
            conn.execute("PRAGMA foreign_keys = ON")
            logger.debug(f"数据库连接已建立: {self.db_path}")
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def initialize_database(self):
        """初始化数据库，创建所有必要的表"""
        try:
            # 确保目录存在
            if not self.ensure_db_directory():
                return False
            
            logger.info("开始初始化数据库...")
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建数据字段表
                self.create_data_fields_table(cursor)
                
                # 创建数据集表
                self.create_datasets_table(cursor)
                
                # 创建用户会话表
                self.create_sessions_table(cursor)
                
                # 提交事务
                conn.commit()
                
            logger.info("数据库初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    def create_data_fields_table(self, cursor):
        """创建数据字段表"""
        sql = """
        CREATE TABLE IF NOT EXISTS data_fields (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            field_id TEXT UNIQUE NOT NULL,
            description TEXT,
            dataset_id TEXT,
            dataset_name TEXT,
            category_id TEXT,
            category_name TEXT,
            subcategory_id TEXT,
            subcategory_name TEXT,
            region TEXT,
            delay INTEGER,
            universe TEXT,
            data_type TEXT,
            coverage REAL,
            user_count INTEGER,
            alpha_count INTEGER,
            themes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(sql)
        logger.debug("数据字段表已创建")

        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_field_id ON data_fields(field_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_dataset_id ON data_fields(dataset_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_category_id ON data_fields(category_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_subcategory_id ON data_fields(subcategory_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_data_type ON data_fields(data_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_region ON data_fields(region)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_fields_universe ON data_fields(universe)")
    
    def create_datasets_table(self, cursor):
        """创建数据集表"""
        sql = """
        CREATE TABLE IF NOT EXISTS datasets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            dataset_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            region TEXT,
            universe TEXT,
            instrument_type TEXT,
            delay INTEGER,
            status TEXT DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(sql)
        logger.debug("数据集表已创建")
        
        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_datasets_dataset_id ON datasets(dataset_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_datasets_status ON datasets(status)")
    
    def create_sessions_table(self, cursor):
        """创建用户会话表"""
        sql = """
        CREATE TABLE IF NOT EXISTS sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            username TEXT,
            session_token TEXT,
            expires_at TIMESTAMP,
            permissions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(sql)
        logger.debug("用户会话表已创建")
        
        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at)")
    
    def get_database_info(self):
        """获取数据库信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取所有表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                info = {
                    "database_path": str(self.db_path),
                    "database_size": self.db_path.stat().st_size if self.db_path.exists() else 0,
                    "tables": tables,
                    "table_counts": {}
                }
                
                # 获取每个表的记录数
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    info["table_counts"][table] = count
                
                return info
                
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return None


# 全局数据库管理器实例
db_manager = DatabaseManager()


def init_database():
    """初始化数据库的便捷函数"""
    return db_manager.initialize_database()


def check_database():
    """检查数据库是否存在的便捷函数"""
    return db_manager.database_exists()


def get_db_connection():
    """获取数据库连接的便捷函数"""
    return db_manager.get_connection()


def get_db_info():
    """获取数据库信息的便捷函数"""
    return db_manager.get_database_info()


def insert_data_field(field_data):
    """插入数据字段记录的便捷函数"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            sql = """
            INSERT OR REPLACE INTO data_fields
            (field_id, description, dataset_id, dataset_name, category_id, category_name,
             subcategory_id, subcategory_name, region, delay, universe, data_type,
             coverage, user_count, alpha_count, themes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            # 处理 themes 数组，转换为 JSON 字符串
            themes_str = None
            if field_data.get('themes'):
                import json
                themes_str = json.dumps(field_data.get('themes'))

            cursor.execute(sql, (
                field_data.get('id'),
                field_data.get('description'),
                field_data.get('dataset', {}).get('id') if field_data.get('dataset') else None,
                field_data.get('dataset', {}).get('name') if field_data.get('dataset') else None,
                field_data.get('category', {}).get('id') if field_data.get('category') else None,
                field_data.get('category', {}).get('name') if field_data.get('category') else None,
                field_data.get('subcategory', {}).get('id') if field_data.get('subcategory') else None,
                field_data.get('subcategory', {}).get('name') if field_data.get('subcategory') else None,
                field_data.get('region'),
                field_data.get('delay'),
                field_data.get('universe'),
                field_data.get('type'),
                field_data.get('coverage'),
                field_data.get('userCount'),
                field_data.get('alphaCount'),
                themes_str
            ))

            conn.commit()
            return cursor.lastrowid

    except Exception as e:
        logger.error(f"插入数据字段失败: {e}")
        return None


def get_data_fields_count():
    """获取数据字段表记录数的便捷函数"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM data_fields")
            return cursor.fetchone()[0]
    except Exception as e:
        logger.error(f"获取数据字段数量失败: {e}")
        return 0


def batch_insert_data_fields(fields_list):
    """批量插入数据字段记录的便捷函数"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            sql = """
            INSERT OR REPLACE INTO data_fields
            (field_id, description, dataset_id, dataset_name, category_id, category_name,
             subcategory_id, subcategory_name, region, delay, universe, data_type,
             coverage, user_count, alpha_count, themes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            import json
            data_to_insert = []

            for field_data in fields_list:
                # 处理 themes 数组，转换为 JSON 字符串
                themes_str = None
                if field_data.get('themes'):
                    themes_str = json.dumps(field_data.get('themes'))

                data_to_insert.append((
                    field_data.get('id'),
                    field_data.get('description'),
                    field_data.get('dataset', {}).get('id') if field_data.get('dataset') else None,
                    field_data.get('dataset', {}).get('name') if field_data.get('dataset') else None,
                    field_data.get('category', {}).get('id') if field_data.get('category') else None,
                    field_data.get('category', {}).get('name') if field_data.get('category') else None,
                    field_data.get('subcategory', {}).get('id') if field_data.get('subcategory') else None,
                    field_data.get('subcategory', {}).get('name') if field_data.get('subcategory') else None,
                    field_data.get('region'),
                    field_data.get('delay'),
                    field_data.get('universe'),
                    field_data.get('type'),
                    field_data.get('coverage'),
                    field_data.get('userCount'),
                    field_data.get('alphaCount'),
                    themes_str
                ))

            cursor.executemany(sql, data_to_insert)
            conn.commit()

            logger.info(f"批量插入 {len(data_to_insert)} 条数据字段记录成功")
            return len(data_to_insert)

    except Exception as e:
        logger.error(f"批量插入数据字段失败: {e}")
        return 0
