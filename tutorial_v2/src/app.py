#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tutorial V2 主应用程序
集成登录功能和全局日志配置
"""

import sys
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.append(str(Path(__file__).parent))

from lib.logger import get_logger, LoggerConfig
from lib.session import get_session, get_session_data, session_manager
from lib.datafields import get_data_fields_cached
from lib.db import check_database, init_database, get_db_info

# 获取应用程序日志器
logger = get_logger(__name__)


def main():
    """
    主函数
    执行应用程序的主要逻辑
    """
    # 设置日志级别
    LoggerConfig.set_level("INFO")

    try:
        # 数据库初始化检查
        LoggerConfig.print_info("检查数据库状态...")
        if not check_database():
            LoggerConfig.print_warning("数据库不存在，开始初始化...")
            if init_database():
                LoggerConfig.print_success("数据库初始化完成")
            else:
                LoggerConfig.print_error("数据库初始化失败")
                return False
        else:
            LoggerConfig.print_success("数据库已存在")

        # 显示数据库信息
        db_info = get_db_info()
        if db_info:
            logger.info(f"数据库路径: {db_info['database_path']}")
            logger.info(f"数据库大小: {db_info['database_size']} bytes")
            logger.info(f"数据表: {', '.join(db_info['tables'])}")

        # 使用全局session管理器获取会话
        LoggerConfig.print_info("开始获取认证会话...")

        # 获取session（自动处理登录、超时检查和重试）
        session = get_session()
        session_data = get_session_data()
        
        if session and session_data:
            LoggerConfig.print_success("会话获取成功！")
            logger.info(f"已获得认证会话，用户ID: {session_data.get('user', {}).get('id', 'unknown')}")
            logger.info(f"用户权限: {session_data.get('permissions', [])}")

            # 获取 data_fields
            data_fields = get_data_fields_cached()
            # 这里可以添加登录成功后的其他操作
            # 例如：数据获取、处理等
            
        else:
            LoggerConfig.print_error("会话获取失败")
            logger.error("未能获得有效的认证会话")
            return False
            
    except Exception as e:
        logger.exception("应用程序执行过程中发生异常")
        LoggerConfig.print_error(f"程序异常: {str(e)}")
        return False
    
    finally:
        logger.info("-" * 60)
        logger.info("Tutorial V2 应用程序结束")
        logger.info("-" * 60)
    
    return True


def run_app():
    """
    应用程序入口函数
    包含错误处理和退出码管理
    """
    try:
        success = main()
        if success:
            LoggerConfig.print_success("应用程序执行完成")
            sys.exit(0)
        else:
            LoggerConfig.print_error("应用程序执行失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        LoggerConfig.print_warning("用户中断了程序执行")
        logger.warning("收到键盘中断信号")
        sys.exit(130)
        
    except Exception as e:
        logger.exception("应用程序发生未处理的异常")
        LoggerConfig.print_error(f"严重错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    run_app()